# word_jumble

A new Flutter project.

## Getting Started

# Xếp Chữ Tiếng Việt - Vietnamese Word Jumble Game

Trò chơi xếp chữ tiếng Việt giúp bạn học từ vựng và cải thiện khả năng sử dụng tiếng Việt một cách vui vẻ.

## Tính năng chính

### 🎮 Chế độ chơi
- **Chơi Nhanh**: 60 giây để tìm ra càng nhiều từ càng tốt
- **Chơi Theo Cấp**: Vượt qua từng level với độ khó tăng dần
- **Thử Thách Hàng Ngày**: Th<PERSON> thách mới mỗi ngày

### 🎯 C<PERSON> chế trò chơi
- Xáo trộn các chữ cái trong từ tiếng Việt (bao gồm dấu thanh và dấu mũ)
- Ng<PERSON><PERSON><PERSON> chơi sắp xếp lại để tạo thành từ đúng
- Hiển thị nghĩa của từ để gợi ý
- Hệ thống điểm thưởng dựa trên độ khó và thời gian

### 📊 Tính năng khác
- Thống kê chi tiết về tiến độ học tập
- Lưu trữ từ đã học và high scores
- Gợi ý khi gặp khó khăn
- Hiệu ứng âm thanh và animation đẹp mắt
- Cài đặt tùy chỉnh

## Cài đặt và chạy

### Yêu cầu
- Flutter SDK (>= 3.6.0)
- Dart SDK
- Android Studio hoặc VS Code
- Thiết bị Android/iOS hoặc emulator

### Các bước cài đặt

1. **Clone repository**
   ```bash
   git clone <repository-url>
   cd word_jumble
   ```

2. **Cài đặt dependencies**
   ```bash
   flutter pub get
   ```

3. **Chạy ứng dụng**
   ```bash
   flutter run
   ```

### Dependencies chính
- `provider`: Quản lý state
- `shared_preferences`: Lưu trữ dữ liệu local
- `audioplayers`: Phát âm thanh
- `flutter_animate`: Animation effects
- `flutter_tts`: Text-to-speech (tùy chọn)

## Cấu trúc dự án

```
lib/
├── main.dart                 # Entry point
├── models/                   # Data models
│   ├── word.dart
│   ├── game_state.dart
│   └── player.dart
├── services/                 # Business logic
│   ├── game_service.dart
│   ├── word_service.dart
│   └── storage_service.dart
├── screens/                  # UI screens
│   ├── home_screen.dart
│   ├── game_screen.dart
│   ├── game_result_screen.dart
│   ├── stats_screen.dart
│   └── settings_screen.dart
└── widgets/                  # Reusable widgets
    ├── letter_tile.dart
    ├── drop_zone.dart
    ├── game_timer.dart
    └── score_display.dart

assets/
├── data/
│   └── vietnamese_words.json # Từ điển tiếng Việt
├── images/                   # Hình ảnh
└── sounds/                   # Âm thanh
```

## Cách chơi

1. **Chọn chế độ chơi** từ màn hình chính
2. **Đọc nghĩa của từ** được hiển thị
3. **Sắp xếp các chữ cái** bằng cách:
   - Chạm vào chữ cái để thêm vào đáp án
   - Kéo thả để sắp xếp lại
   - Chạm vào chữ cái trong đáp án để xóa
4. **Nhấn "Kiểm tra"** khi hoàn thành
5. **Sử dụng gợi ý** nếu cần thiết

## Đóng góp

Chúng tôi hoan nghênh mọi đóng góp! Vui lòng:

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Tạo Pull Request

## License

Dự án này được phân phối dưới MIT License. Xem file `LICENSE` để biết thêm chi tiết.

## Liên hệ

- Email: <EMAIL>
- Website: https://wordgame.com

---

**Chúc bạn chơi game vui vẻ và học được nhiều từ mới! 🎉**
