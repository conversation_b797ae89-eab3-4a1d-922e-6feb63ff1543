import 'package:flutter/material.dart';
import '../services/storage_service.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final StorageService _storageService = StorageService();
  
  bool _soundEnabled = true;
  bool _musicEnabled = true;
  bool _vibrationEnabled = true;
  int _difficulty = 1;
  String _theme = 'light';

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final settings = await _storageService.getGameSettings();
    setState(() {
      _soundEnabled = settings['soundEnabled'] ?? true;
      _musicEnabled = settings['musicEnabled'] ?? true;
      _vibrationEnabled = settings['vibrationEnabled'] ?? true;
      _difficulty = settings['difficulty'] ?? 1;
      _theme = settings['theme'] ?? 'light';
    });
  }

  Future<void> _saveSettings() async {
    final settings = {
      'soundEnabled': _soundEnabled,
      'musicEnabled': _musicEnabled,
      'vibrationEnabled': _vibrationEnabled,
      'difficulty': _difficulty,
      'theme': _theme,
    };
    await _storageService.saveGameSettings(settings);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cài Đặt'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF667eea),
              Color(0xFF764ba2),
            ],
          ),
        ),
        child: SafeArea(
          child: ListView(
            padding: const EdgeInsets.all(20),
            children: [
              _buildSectionCard(
                'Âm thanh & Hiệu ứng',
                [
                  _buildSwitchTile(
                    'Âm thanh',
                    'Bật/tắt hiệu ứng âm thanh',
                    Icons.volume_up,
                    _soundEnabled,
                    (value) {
                      setState(() => _soundEnabled = value);
                      _saveSettings();
                    },
                  ),
                  _buildSwitchTile(
                    'Nhạc nền',
                    'Bật/tắt nhạc nền',
                    Icons.music_note,
                    _musicEnabled,
                    (value) {
                      setState(() => _musicEnabled = value);
                      _saveSettings();
                    },
                  ),
                  _buildSwitchTile(
                    'Rung',
                    'Bật/tắt rung khi tương tác',
                    Icons.vibration,
                    _vibrationEnabled,
                    (value) {
                      setState(() => _vibrationEnabled = value);
                      _saveSettings();
                    },
                  ),
                ],
              ),
              const SizedBox(height: 20),
              _buildSectionCard(
                'Trò chơi',
                [
                  _buildDifficultyTile(),
                  _buildThemeTile(),
                ],
              ),
              const SizedBox(height: 20),
              _buildSectionCard(
                'Dữ liệu',
                [
                  _buildActionTile(
                    'Xóa dữ liệu',
                    'Xóa tất cả tiến độ và thống kê',
                    Icons.delete_forever,
                    Colors.red,
                    _showClearDataDialog,
                  ),
                  _buildActionTile(
                    'Xuất dữ liệu',
                    'Sao lưu tiến độ của bạn',
                    Icons.backup,
                    Colors.blue,
                    _exportData,
                  ),
                ],
              ),
              const SizedBox(height: 20),
              _buildSectionCard(
                'Thông tin',
                [
                  _buildInfoTile('Phiên bản', '1.0.0'),
                  _buildActionTile(
                    'Giới thiệu',
                    'Về ứng dụng Xếp Chữ Tiếng Việt',
                    Icons.info,
                    Colors.grey,
                    _showAboutDialog,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionCard(String title, List<Widget> children) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    IconData icon,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return ListTile(
      leading: Icon(icon, color: Colors.blue),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: Colors.blue,
      ),
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildDifficultyTile() {
    return ListTile(
      leading: const Icon(Icons.tune, color: Colors.orange),
      title: const Text('Độ khó mặc định'),
      subtitle: Text(_getDifficultyText(_difficulty)),
      trailing: DropdownButton<int>(
        value: _difficulty,
        items: const [
          DropdownMenuItem(value: 1, child: Text('Dễ')),
          DropdownMenuItem(value: 2, child: Text('Vừa')),
          DropdownMenuItem(value: 3, child: Text('Khó')),
        ],
        onChanged: (value) {
          if (value != null) {
            setState(() => _difficulty = value);
            _saveSettings();
          }
        },
      ),
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildThemeTile() {
    return ListTile(
      leading: const Icon(Icons.palette, color: Colors.purple),
      title: const Text('Giao diện'),
      subtitle: Text(_theme == 'light' ? 'Sáng' : 'Tối'),
      trailing: DropdownButton<String>(
        value: _theme,
        items: const [
          DropdownMenuItem(value: 'light', child: Text('Sáng')),
          DropdownMenuItem(value: 'dark', child: Text('Tối')),
        ],
        onChanged: (value) {
          if (value != null) {
            setState(() => _theme = value);
            _saveSettings();
          }
        },
      ),
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildActionTile(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(icon, color: color),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildInfoTile(String title, String value) {
    return ListTile(
      title: Text(title),
      trailing: Text(
        value,
        style: const TextStyle(
          color: Colors.grey,
          fontWeight: FontWeight.w500,
        ),
      ),
      contentPadding: EdgeInsets.zero,
    );
  }

  String _getDifficultyText(int difficulty) {
    switch (difficulty) {
      case 1:
        return 'Từ đơn giản, dễ hiểu';
      case 2:
        return 'Từ trung bình, vừa phải';
      case 3:
        return 'Từ phức tạp, khó';
      default:
        return 'Dễ';
    }
  }

  void _showClearDataDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xóa dữ liệu'),
        content: const Text(
          'Bạn có chắc chắn muốn xóa tất cả tiến độ và thống kê? '
          'Hành động này không thể hoàn tác.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final messenger = ScaffoldMessenger.of(context);
              await _storageService.clearAllData();
              navigator.pop();
              messenger.showSnackBar(
                const SnackBar(
                  content: Text('Đã xóa tất cả dữ liệu'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Xóa', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _exportData() {
    // Tính năng xuất dữ liệu - có thể implement sau
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Tính năng sẽ được cập nhật trong phiên bản tới'),
      ),
    );
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Về ứng dụng'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Xếp Chữ Tiếng Việt',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text('Phiên bản: 1.0.0'),
            SizedBox(height: 16),
            Text(
              'Trò chơi xếp chữ tiếng Việt giúp bạn học từ vựng '
              'và cải thiện khả năng sử dụng tiếng Việt một cách vui vẻ.',
            ),
            SizedBox(height: 16),
            Text(
              'Phát triển bởi: Flutter Team',
              style: TextStyle(fontStyle: FontStyle.italic),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Đóng'),
          ),
        ],
      ),
    );
  }
}
