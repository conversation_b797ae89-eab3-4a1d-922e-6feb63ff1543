import 'package:flutter/material.dart';
import '../models/player.dart';
import '../services/storage_service.dart';

class StatsScreen extends StatefulWidget {
  final PlayerStats playerStats;

  const StatsScreen({super.key, required this.playerStats});

  @override
  State<StatsScreen> createState() => _StatsScreenState();
}

class _StatsScreenState extends State<StatsScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late List<Animation<double>> _progressAnimations;
  
  Map<int, int> _levelHighScores = {};
  int _totalPlayTime = 0;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadAdditionalData();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _progressAnimations = List.generate(4, (index) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: _animationController,
          curve: Interval(
            index * 0.15,
            (index * 0.15) + 0.4,
            curve: Curves.easeOutCubic,
          ),
        ),
      );
    });
    
    _animationController.forward();
  }

  Future<void> _loadAdditionalData() async {
    final storageService = StorageService();
    final highScores = await storageService.getAllHighScores();
    final playTime = await storageService.getTotalPlayTime();
    
    setState(() {
      _levelHighScores = highScores;
      _totalPlayTime = playTime;
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Thống Kê'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF667eea),
              Color(0xFF764ba2),
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildOverviewCards(),
                const SizedBox(height: 24),
                _buildProgressSection(),
                const SizedBox(height: 24),
                _buildLevelHighScores(),
                const SizedBox(height: 24),
                _buildLearnedWords(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOverviewCards() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.2,
      children: [
        _buildStatCard(
          'Tổng điểm',
          widget.playerStats.totalScore.toString(),
          Icons.star,
          Colors.orange,
          0,
        ),
        _buildStatCard(
          'Điểm cao nhất',
          widget.playerStats.highScore.toString(),
          Icons.emoji_events,
          Colors.amber,
          1,
        ),
        _buildStatCard(
          'Game đã chơi',
          widget.playerStats.totalGamesPlayed.toString(),
          Icons.games,
          Colors.blue,
          2,
        ),
        _buildStatCard(
          'Từ đã học',
          widget.playerStats.learnedWords.length.toString(),
          Icons.book,
          Colors.green,
          3,
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color, int animationIndex) {
    return AnimatedBuilder(
      animation: _progressAnimations[animationIndex],
      builder: (context, child) {
        return Transform.scale(
          scale: _progressAnimations[animationIndex].value.clamp(0.0, 1.0),
          child: Card(
            elevation: 8,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                gradient: LinearGradient(
                  colors: [color.withValues(alpha: 0.8), color],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(icon, color: Colors.white, size: 32),
                  const SizedBox(height: 8),
                  Text(
                    value,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.white70,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildProgressSection() {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Tiến độ',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            _buildProgressItem(
              'Độ chính xác',
              (widget.playerStats.averageAccuracy).clamp(0.0, 1.0),
              Colors.blue,
              '${(widget.playerStats.averageAccuracy * 100).toStringAsFixed(1)}%',
            ),
            const SizedBox(height: 16),
            _buildProgressItem(
              'Chuỗi dài nhất',
              (widget.playerStats.longestStreak / 20.0).clamp(0.0, 1.0), // Giả sử max streak là 20
              Colors.orange,
              '${widget.playerStats.longestStreak} từ',
            ),
            const SizedBox(height: 16),
            _buildProgressItem(
              'Thời gian chơi',
              ((_totalPlayTime / 3600) / 100).clamp(0.0, 1.0), // Giả sử mục tiêu là 100 giờ
              Colors.green,
              _formatPlayTime(_totalPlayTime),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressItem(String label, double progress, Color color, String valueText) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              valueText,
              style: TextStyle(
                fontSize: 14,
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return LinearProgressIndicator(
              value: (progress * _animationController.value).clamp(0.0, 1.0),
              backgroundColor: color.withValues(alpha: 0.2),
              valueColor: AlwaysStoppedAnimation<Color>(color),
              minHeight: 8,
            );
          },
        ),
      ],
    );
  }

  Widget _buildLevelHighScores() {
    if (_levelHighScores.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Điểm cao theo Level',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...(_levelHighScores.entries.take(5).map((entry) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('Level ${entry.key}'),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade100,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        entry.value.toString(),
                        style: TextStyle(
                          color: Colors.blue.shade700,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }).toList()),
          ],
        ),
      ),
    );
  }

  Widget _buildLearnedWords() {
    if (widget.playerStats.learnedWords.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Từ đã học (${widget.playerStats.learnedWords.length})',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: widget.playerStats.learnedWords.take(20).map((word) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.green.shade50,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: Colors.green.shade200),
                  ),
                  child: Text(
                    word,
                    style: TextStyle(
                      color: Colors.green.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
            ),
            if (widget.playerStats.learnedWords.length > 20)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  'và ${widget.playerStats.learnedWords.length - 20} từ khác...',
                  style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 12,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  String _formatPlayTime(int seconds) {
    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }
}
