import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/game_state.dart';
import '../services/game_service.dart';

import '../widgets/drop_zone.dart';
import '../widgets/game_timer.dart';
import '../widgets/score_display.dart';
import '../widgets/high_score_dialog.dart';
import 'game_result_screen.dart';

class GameScreen extends StatefulWidget {
  final GameMode mode;

  const GameScreen({super.key, required this.mode});

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> with TickerProviderStateMixin {
  late AnimationController _shakeController;
  late Animation<double> _shakeAnimation;
  late AnimationController _successController;
  late Animation<double> _successAnimation;
  
  List<String> _scrambledLetters = [];
  List<String> _userAnswer = [];
  bool _showHint = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeGame();
  }

  void _setupAnimations() {
    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _shakeAnimation = Tween<double>(
      begin: 0,
      end: 10,
    ).animate(CurvedAnimation(
      parent: _shakeController,
      curve: Curves.elasticIn,
    ));

    _successController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _successAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _successController,
      curve: Curves.elasticOut,
    ));
  }

  Future<void> _initializeGame() async {
    final gameService = Provider.of<GameService>(context, listen: false);
    await gameService.initialize();
    await gameService.startNewGame(widget.mode);
    _updateScrambledLetters();
  }

  void _updateScrambledLetters() {
    final gameService = Provider.of<GameService>(context, listen: false);
    final currentWord = gameService.gameState.currentWord;
    
    if (currentWord != null) {
      setState(() {
        _scrambledLetters = currentWord.scrambled.split('');
        _userAnswer.clear();
        _showHint = false;
      });
    }
  }

  @override
  void dispose() {
    _shakeController.dispose();
    _successController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_getGameModeTitle()),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.pause),
            onPressed: _pauseGame,
          ),
        ],
      ),
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF4facfe),
              Color(0xFF00f2fe),
            ],
          ),
        ),
        child: SafeArea(
          child: Consumer<GameService>(
            builder: (context, gameService, child) {
              final gameState = gameService.gameState;
              
              // Kiểm tra game over
              if (gameState.isGameOver) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  _navigateToResult(gameState);
                });
              }
              
              return Column(
                children: [
                  _buildGameHeader(gameState),
                  const SizedBox(height: 20),
                  _buildWordMeaning(gameState),
                  const SizedBox(height: 30),
                  _buildDropZone(),
                  const SizedBox(height: 40),
                  _buildScrambledLetters(),
                  const Spacer(),
                  _buildActionButtons(gameService),
                  const SizedBox(height: 20),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildGameHeader(GameState gameState) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          ScoreDisplay(score: gameState.score),
          // Hiển thị timer khác nhau tùy theo mode
          gameState.mode == GameMode.levelMode
            ? _buildLevelModeTimer(gameState)
            : GameTimer(timeRemaining: gameState.timeRemaining),
          Column(
            children: [
              Text(
                'Level ${gameState.level}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'Streak: ${gameState.streak}',
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLevelModeTimer(GameState gameState) {
    return Column(
      children: [
        // Timer cho từ hiện tại
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: gameState.wordTimeRemaining <= 5
              ? Colors.red.withValues(alpha: 0.8)
              : Colors.blue.withValues(alpha: 0.8),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            '${gameState.wordTimeRemaining}s',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 4),
        // Timer tổng (nhỏ hơn)
        Text(
          'Tổng: ${gameState.timeRemaining}s',
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  Widget _buildWordMeaning(GameState gameState) {
    if (gameState.currentWord == null) return const SizedBox.shrink();
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          const Text(
            'Nghĩa của từ:',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            gameState.currentWord!.meaning,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          if (_showHint) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'Gợi ý: ${Provider.of<GameService>(context, listen: false).getHint()}',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.orange,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDropZone() {
    return AnimatedBuilder(
      animation: _shakeAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(_shakeAnimation.value, 0),
          child: ScaleTransition(
            scale: _successAnimation,
            child: DropZone(
              letters: _userAnswer,
              onReorder: _reorderAnswer,
              onRemoveLetter: _removeLetterFromAnswer,
            ),
          ),
        );
      },
    );
  }

  Widget _buildScrambledLetters() {
    return Container(
      height: 80,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Wrap(
        spacing: 8,
        runSpacing: 8,
        alignment: WrapAlignment.center,
        children: _scrambledLetters.asMap().entries.map((entry) {
          int index = entry.key;
          String letter = entry.value;
          
          return GestureDetector(
            onTap: () => _addLetterToAnswer(letter, index),
            child: Container(
              margin: const EdgeInsets.all(4),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Text(
                letter,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildActionButtons(GameService gameService) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _showHint ? null : _showHintAction,
              icon: const Icon(Icons.lightbulb_outline),
              label: const Text('Gợi ý'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _userAnswer.isEmpty ? null : _clearAnswer,
              icon: const Icon(Icons.clear),
              label: const Text('Xóa'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _checkAnswer,
              icon: const Icon(Icons.check),
              label: const Text('Kiểm tra'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getGameModeTitle() {
    switch (widget.mode) {
      case GameMode.quickPlay:
        return 'Chơi Nhanh';
      case GameMode.levelMode:
        return 'Chơi Theo Cấp';
      case GameMode.dailyChallenge:
        return 'Thử Thách Hàng Ngày';
    }
  }

  void _addLetterToAnswer(String letter, int index) {
    setState(() {
      _userAnswer.add(letter);
      _scrambledLetters.removeAt(index);
    });
  }

  void _removeLetterFromAnswer(int index) {
    if (index < _userAnswer.length) {
      setState(() {
        String letter = _userAnswer.removeAt(index);
        _scrambledLetters.add(letter);
      });
    }
  }

  void _reorderAnswer(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final String letter = _userAnswer.removeAt(oldIndex);
      _userAnswer.insert(newIndex, letter);
    });
  }

  void _clearAnswer() {
    setState(() {
      _scrambledLetters.addAll(_userAnswer);
      _userAnswer.clear();
    });
  }

  void _showHintAction() {
    setState(() {
      _showHint = true;
    });
  }

  void _checkAnswer() {
    final gameService = Provider.of<GameService>(context, listen: false);
    final userInput = _userAnswer.join('');

    // Cho phép kiểm tra ngay cả khi chưa điền đủ
    if (userInput.isEmpty) {
      // Hiển thị shake animation nếu chưa điền gì
      _shakeController.forward().then((_) {
        _shakeController.reverse();
      });
      return;
    }

    gameService.updateUserInput(userInput);
    bool isCorrect = gameService.checkAnswer();

    if (isCorrect) {
      _successController.forward().then((_) {
        _successController.reverse();
        _updateScrambledLetters();
      });
    } else {
      _shakeController.forward().then((_) {
        _shakeController.reverse();
      });
    }
  }

  void _pauseGame() {
    final gameService = Provider.of<GameService>(context, listen: false);
    gameService.pauseGame();
    
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Game Tạm Dừng'),
        content: const Text('Game đã được tạm dừng. Bạn có muốn tiếp tục?'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            child: const Text('Thoát'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              gameService.resumeGame();
            },
            child: const Text('Tiếp tục'),
          ),
        ],
      ),
    );
  }

  void _navigateToResult(GameState gameState) async {
    final gameService = Provider.of<GameService>(context, listen: false);

    // Kiểm tra xem có phải high score không
    if (gameService.isHighScore()) {
      final highScoreData = gameService.getHighScoreData();

      // Hiển thị dialog high score
      await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (context) => HighScoreDialog(
          score: highScoreData['score'],
          level: highScoreData['level'],
          difficulty: highScoreData['difficulty'],
          wordsCompleted: highScoreData['wordsCompleted'],
          accuracy: highScoreData['accuracy'],
          timeSpent: highScoreData['timeSpent'],
        ),
      );
    }

    // Chuyển đến màn hình kết quả
    if (mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => GameResultScreen(
            gameState: gameState,
            mode: widget.mode,
          ),
        ),
      );
    }
  }
}
