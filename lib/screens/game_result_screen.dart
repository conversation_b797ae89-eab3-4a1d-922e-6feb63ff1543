import 'package:flutter/material.dart';
import '../models/game_state.dart';
import '../models/player.dart';
import '../services/storage_service.dart';

class GameResultScreen extends StatefulWidget {
  final GameState gameState;
  final GameMode mode;

  const GameResultScreen({
    super.key,
    required this.gameState,
    required this.mode,
  });

  @override
  State<GameResultScreen> createState() => _GameResultScreenState();
}

class _GameResultScreenState extends State<GameResultScreen>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _bounceController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _bounceAnimation;
  
  PlayerStats? _updatedStats;
  bool _isNewHighScore = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _updatePlayerStats();
  }

  void _setupAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));
    
    _bounceAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: Curves.elasticOut,
    ));
    
    _slideController.forward();
    Future.delayed(const Duration(milliseconds: 300), () {
      _bounceController.forward();
    });
  }

  Future<void> _updatePlayerStats() async {
    final storageService = StorageService();
    final currentStats = await storageService.getPlayerStats();
    
    _isNewHighScore = widget.gameState.score > currentStats.highScore;
    
    final updatedStats = currentStats.updateAfterGame(
      gameScore: widget.gameState.score,
      wordsCompleted: widget.gameState.correctAnswers,
      gameAccuracy: widget.gameState.accuracy,
      gameStreak: widget.gameState.streak,
      level: widget.gameState.level,
    );
    
    // Thêm từ đã học
    PlayerStats finalStats = updatedStats;
    for (final word in widget.gameState.completedWords) {
      finalStats = finalStats.addLearnedWord(word.original);
    }
    
    await storageService.savePlayerStats(finalStats);
    
    // Lưu high score cho level
    await storageService.saveHighScore(widget.gameState.level, widget.gameState.score);
    
    // Lưu daily challenge nếu cần
    if (widget.mode == GameMode.dailyChallenge) {
      await storageService.saveDailyChallengeInfo(
        date: DateTime.now(),
        completed: true,
        score: widget.gameState.score,
      );
    }
    
    setState(() {
      _updatedStats = finalStats;
    });
  }

  @override
  void dispose() {
    _slideController.dispose();
    _bounceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF667eea),
              Color(0xFF764ba2),
            ],
          ),
        ),
        child: SafeArea(
          child: SlideTransition(
            position: _slideAnimation,
            child: Column(
              children: [
                _buildHeader(),
                Expanded(
                  child: _buildResultContent(),
                ),
                _buildActionButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          ScaleTransition(
            scale: _bounceAnimation,
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                shape: BoxShape.circle,
              ),
              child: Icon(
                _isNewHighScore ? Icons.emoji_events : Icons.check_circle,
                size: 60,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            _isNewHighScore ? 'Kỷ lục mới!' : 'Hoàn thành!',
            style: const TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          Text(
            _getGameModeTitle(),
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultContent() {
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildScoreSection(),
          const Divider(height: 32),
          _buildStatsSection(),
          const Divider(height: 32),
          _buildWordsSection(),
        ],
      ),
    );
  }

  Widget _buildScoreSection() {
    return Column(
      children: [
        const Text(
          'Điểm số',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          widget.gameState.score.toString(),
          style: TextStyle(
            fontSize: 48,
            fontWeight: FontWeight.bold,
            color: _isNewHighScore ? Colors.orange : Colors.blue,
          ),
        ),
        if (_isNewHighScore)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.orange,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Text(
              'KỶ LỤC MỚI!',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildStatsSection() {
    return Row(
      children: [
        Expanded(
          child: _buildStatItem(
            'Từ đúng',
            widget.gameState.correctAnswers.toString(),
            Icons.check_circle,
            Colors.green,
          ),
        ),
        Expanded(
          child: _buildStatItem(
            'Từ sai',
            widget.gameState.wrongAnswers.toString(),
            Icons.cancel,
            Colors.red,
          ),
        ),
        Expanded(
          child: _buildStatItem(
            'Độ chính xác',
            '${(widget.gameState.accuracy * 100).toStringAsFixed(1)}%',
            Icons.track_changes,
            Colors.blue,
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildWordsSection() {
    if (widget.gameState.completedWords.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Từ đã hoàn thành:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: widget.gameState.completedWords.take(10).map((word) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Text(
                word.original,
                style: TextStyle(
                  color: Colors.blue.shade700,
                  fontWeight: FontWeight.w500,
                ),
              ),
            );
          }).toList(),
        ),
        if (widget.gameState.completedWords.length > 10)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              'và ${widget.gameState.completedWords.length - 10} từ khác...',
              style: const TextStyle(
                color: Colors.grey,
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton(
              onPressed: () => Navigator.of(context).popUntil((route) => route.isFirst),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: Colors.grey.shade700,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'Về trang chủ',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _playAgain,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'Chơi lại',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getGameModeTitle() {
    switch (widget.mode) {
      case GameMode.quickPlay:
        return 'Chơi Nhanh';
      case GameMode.levelMode:
        return 'Chơi Theo Cấp - Level ${widget.gameState.level}';
      case GameMode.dailyChallenge:
        return 'Thử Thách Hàng Ngày';
    }
  }

  void _playAgain() {
    Navigator.of(context).popUntil((route) => route.isFirst);
    // Có thể thêm logic để bắt đầu game mới ngay lập tức
  }
}
