import 'package:flutter/material.dart';

class LetterTile extends StatefulWidget {
  final String letter;
  final bool isUsed;
  final VoidCallback? onTap;
  final bool isDraggable;

  const LetterTile({
    super.key,
    required this.letter,
    this.isUsed = false,
    this.onTap,
    this.isDraggable = false,
  });

  @override
  State<LetterTile> createState() => _LetterTileState();
}

class _LetterTileState extends State<LetterTile> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isDraggable) {
      return _buildDraggableTile();
    } else {
      return _buildTappableTile();
    }
  }

  Widget _buildTappableTile() {
    return GestureDetector(
      onTapDown: (_) {
        setState(() => _isPressed = true);
        _animationController.forward();
      },
      onTapUp: (_) {
        setState(() => _isPressed = false);
        _animationController.reverse();
        widget.onTap?.call();
      },
      onTapCancel: () {
        setState(() => _isPressed = false);
        _animationController.reverse();
      },
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: _buildTileContent(),
          );
        },
      ),
    );
  }

  Widget _buildDraggableTile() {
    return Draggable<String>(
      data: widget.letter,
      feedback: Material(
        color: Colors.transparent,
        child: Transform.scale(
          scale: 1.1,
          child: _buildTileContent(isDragging: true),
        ),
      ),
      childWhenDragging: Opacity(
        opacity: 0.5,
        child: _buildTileContent(),
      ),
      child: _buildTileContent(),
    );
  }

  Widget _buildTileContent({bool isDragging = false}) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: widget.isUsed
              ? [Colors.grey.shade300, Colors.grey.shade400]
              : isDragging
                  ? [Colors.blue.shade300, Colors.blue.shade500]
                  : [Colors.white, Colors.grey.shade100],
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: isDragging ? 8 : 4,
            offset: Offset(0, isDragging ? 4 : 2),
          ),
        ],
        border: Border.all(
          color: widget.isUsed
              ? Colors.grey.shade400
              : isDragging
                  ? Colors.blue.shade600
                  : Colors.grey.shade300,
          width: 2,
        ),
      ),
      child: Center(
        child: Text(
          widget.letter,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: widget.isUsed
                ? Colors.grey.shade600
                : isDragging
                    ? Colors.white
                    : Colors.black87,
          ),
        ),
      ),
    );
  }
}

class DraggableLetterTile extends StatelessWidget {
  final String letter;
  final int index;
  final VoidCallback? onRemove;

  const DraggableLetterTile({
    super.key,
    required this.letter,
    required this.index,
    this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    return Draggable<Map<String, dynamic>>(
      data: {
        'letter': letter,
        'index': index,
        'source': 'answer',
      },
      feedback: Material(
        color: Colors.transparent,
        child: Transform.scale(
          scale: 1.1,
          child: LetterTile(
            letter: letter,
            isDraggable: false,
          ),
        ),
      ),
      childWhenDragging: Opacity(
        opacity: 0.3,
        child: LetterTile(
          letter: letter,
          isDraggable: false,
        ),
      ),
      child: GestureDetector(
        onTap: onRemove,
        child: LetterTile(
          letter: letter,
          isDraggable: false,
        ),
      ),
    );
  }
}
