import 'package:flutter/material.dart';

class GameTimer extends StatelessWidget {
  final int timeRemaining;
  final bool isWarning;

  const GameTimer({
    super.key,
    required this.timeRemaining,
    this.isWarning = false,
  });

  @override
  Widget build(BuildContext context) {
    final minutes = timeRemaining ~/ 60;
    final seconds = timeRemaining % 60;
    final timeString = '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    
    final isLowTime = timeRemaining <= 30;
    final isCriticalTime = timeRemaining <= 10;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: isCriticalTime
            ? Colors.red.withValues(alpha: 0.9)
            : isLowTime
                ? Colors.orange.withValues(alpha: 0.9)
                : Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.timer,
            color: isCriticalTime || isLowTime ? Colors.white : Colors.grey.shade700,
            size: 20,
          ),
          const SizedBox(width: 8),
          AnimatedDefaultTextStyle(
            duration: const Duration(milliseconds: 300),
            style: TextStyle(
              fontSize: isCriticalTime ? 18 : 16,
              fontWeight: FontWeight.bold,
              color: isCriticalTime || isLowTime ? Colors.white : Colors.grey.shade700,
            ),
            child: Text(timeString),
          ),
        ],
      ),
    );
  }
}

class AnimatedGameTimer extends StatefulWidget {
  final int timeRemaining;

  const AnimatedGameTimer({
    super.key,
    required this.timeRemaining,
  });

  @override
  State<AnimatedGameTimer> createState() => _AnimatedGameTimerState();
}

class _AnimatedGameTimerState extends State<AnimatedGameTimer>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  late AnimationController _shakeController;
  late Animation<double> _shakeAnimation;

  int _previousTime = 0;

  @override
  void initState() {
    super.initState();
    _previousTime = widget.timeRemaining;
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _shakeAnimation = Tween<double>(
      begin: 0,
      end: 5,
    ).animate(CurvedAnimation(
      parent: _shakeController,
      curve: Curves.elasticIn,
    ));

    _startAnimations();
  }

  @override
  void didUpdateWidget(AnimatedGameTimer oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.timeRemaining != _previousTime) {
      _previousTime = widget.timeRemaining;
      _startAnimations();
    }
  }

  void _startAnimations() {
    if (widget.timeRemaining <= 10 && widget.timeRemaining > 0) {
      // Critical time - pulse and shake
      _pulseController.repeat(reverse: true);
      _shakeController.forward().then((_) => _shakeController.reverse());
    } else if (widget.timeRemaining <= 30) {
      // Warning time - just pulse
      _pulseController.repeat(reverse: true);
      _shakeController.stop();
    } else {
      // Normal time - stop animations
      _pulseController.stop();
      _shakeController.stop();
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _shakeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_pulseAnimation, _shakeAnimation]),
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(_shakeAnimation.value, 0),
          child: Transform.scale(
            scale: _pulseAnimation.value,
            child: GameTimer(timeRemaining: widget.timeRemaining),
          ),
        );
      },
    );
  }
}
