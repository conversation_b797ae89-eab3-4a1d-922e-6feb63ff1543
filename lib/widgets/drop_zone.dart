import 'package:flutter/material.dart';
import 'letter_tile.dart';

class DropZone extends StatefulWidget {
  final List<String> letters;
  final Function(int oldIndex, int newIndex)? onReorder;
  final Function(int index)? onRemoveLetter;
  final int maxLetters;

  const DropZone({
    super.key,
    required this.letters,
    this.onReorder,
    this.onRemoveLetter,
    this.maxLetters = 15,
  });

  @override
  State<DropZone> createState() => _DropZoneState();
}

class _DropZoneState extends State<DropZone> with TickerProviderStateMixin {
  late AnimationController _highlightController;
  late Animation<Color?> _highlightAnimation;
  bool _isHighlighted = false;

  @override
  void initState() {
    super.initState();
    _highlightController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _highlightAnimation = ColorTween(
      begin: Colors.grey.shade200,
      end: Colors.blue.shade100,
    ).animate(CurvedAnimation(
      parent: _highlightController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _highlightController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          const Text(
            'Kéo thả các chữ cái vào đây:',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 12),
          AnimatedBuilder(
            animation: _highlightAnimation,
            builder: (context, child) {
              return DragTarget<String>(
                onWillAcceptWithDetails: (details) {
                  if (!_isHighlighted) {
                    setState(() => _isHighlighted = true);
                    _highlightController.forward();
                  }
                  return widget.letters.length < widget.maxLetters;
                },
                onLeave: (data) {
                  if (_isHighlighted) {
                    setState(() => _isHighlighted = false);
                    _highlightController.reverse();
                  }
                },
                onAcceptWithDetails: (details) {
                  if (_isHighlighted) {
                    setState(() => _isHighlighted = false);
                    _highlightController.reverse();
                  }
                  // This will be handled by the parent widget
                },
                builder: (context, candidateData, rejectedData) {
                  return Container(
                    width: double.infinity,
                    constraints: const BoxConstraints(
                      minHeight: 80,
                      maxHeight: 160,
                    ),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: _highlightAnimation.value,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: _isHighlighted ? Colors.blue : Colors.grey.shade300,
                        width: 2,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: widget.letters.isEmpty
                        ? _buildEmptyState()
                        : _buildLetterGrid(),
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.touch_app,
            size: 32,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 8),
          Text(
            'Chạm vào chữ cái bên dưới\nhoặc kéo thả vào đây',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLetterGrid() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      alignment: WrapAlignment.center,
      children: widget.letters.asMap().entries.map((entry) {
        int index = entry.key;
        String letter = entry.value;

        return DraggableLetterTile(
          letter: letter,
          index: index,
          onRemove: () => widget.onRemoveLetter?.call(index),
        );
      }).toList(),
    );
  }
}

// Custom ReorderableWrap widget for better drag and drop experience
class ReorderableWrap extends StatefulWidget {
  final List<Widget> children;
  final Function(int oldIndex, int newIndex) onReorder;
  final double spacing;
  final double runSpacing;

  const ReorderableWrap({
    super.key,
    required this.children,
    required this.onReorder,
    this.spacing = 0,
    this.runSpacing = 0,
  });

  @override
  State<ReorderableWrap> createState() => _ReorderableWrapState();
}

class _ReorderableWrapState extends State<ReorderableWrap> {
  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: widget.spacing,
      runSpacing: widget.runSpacing,
      children: widget.children,
    );
  }
}

class ReorderableWrapItem extends StatelessWidget {
  final Widget child;

  const ReorderableWrapItem({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return child;
  }
}
