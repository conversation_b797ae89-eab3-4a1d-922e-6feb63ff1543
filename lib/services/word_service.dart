import 'dart:convert';
import 'dart:math' as math;
import 'package:flutter/services.dart';
import '../models/word.dart';

class WordService {
  static final WordService _instance = WordService._internal();
  factory WordService() => _instance;
  WordService._internal();

  List<Word> _allWords = [];
  final math.Random _random = math.Random();

  /// Khởi tạo và load từ điển
  Future<void> initialize() async {
    if (_allWords.isNotEmpty) return;
    
    try {
      final String jsonString = await rootBundle.loadString('assets/data/vietnamese_words.json');
      final Map<String, dynamic> jsonData = json.decode(jsonString);
      final List<dynamic> wordsJson = jsonData['words'];
      
      _allWords = wordsJson.map((wordJson) => Word.fromJson(wordJson)).toList();
    } catch (e) {
      // Fallback data nếu không load được file
      _allWords = _getFallbackWords();
    }
  }

  /// L<PERSON><PERSON> từ ngẫu nhiên theo độ khó
  Word getRandomWord({int? difficulty}) {
    if (_allWords.isEmpty) {
      throw Exception('Word service not initialized');
    }

    List<Word> filteredWords = difficulty != null
        ? _allWords.where((word) => word.difficulty == difficulty).toList()
        : _allWords;

    if (filteredWords.isEmpty) {
      filteredWords = _allWords;
    }

    return filteredWords[_random.nextInt(filteredWords.length)];
  }

  /// Lấy danh sách từ theo độ khó
  List<Word> getWordsByDifficulty(int difficulty) {
    return _allWords.where((word) => word.difficulty == difficulty).toList();
  }

  /// Lấy từ ngẫu nhiên không trùng với danh sách đã cho
  Word getRandomWordExcluding(List<Word> excludedWords) {
    List<Word> availableWords = _allWords
        .where((word) => !excludedWords.contains(word))
        .toList();

    if (availableWords.isEmpty) {
      // Nếu hết từ thì reset
      availableWords = _allWords;
    }

    return availableWords[_random.nextInt(availableWords.length)];
  }

  /// Lấy từ cho level cụ thể
  List<Word> getWordsForLevel(int level) {
    int difficulty = _getDifficultyForLevel(level);
    List<Word> words = getWordsByDifficulty(difficulty);
    
    // Trộn ngẫu nhiên và lấy số lượng phù hợp
    words.shuffle(_random);
    int wordsCount = _getWordsCountForLevel(level);
    
    return words.take(wordsCount).toList();
  }

  /// Tính độ khó dựa trên level
  int _getDifficultyForLevel(int level) {
    if (level <= 3) return 1; // Dễ
    if (level <= 7) return 2; // Vừa
    return 3; // Khó
  }

  /// Tính số từ cho mỗi level
  int _getWordsCountForLevel(int level) {
    return math.min(5 + level, 15); // Từ 6 đến 15 từ
  }

  /// Tìm từ theo từ khóa
  List<Word> searchWords(String query) {
    if (query.isEmpty) return [];
    
    String lowerQuery = query.toLowerCase();
    return _allWords.where((word) {
      return word.original.toLowerCase().contains(lowerQuery) ||
             word.meaning.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// Lấy tất cả từ
  List<Word> get allWords => List.unmodifiable(_allWords);

  /// Lấy số lượng từ theo độ khó
  Map<int, int> getWordCountByDifficulty() {
    Map<int, int> counts = {1: 0, 2: 0, 3: 0};
    for (Word word in _allWords) {
      counts[word.difficulty] = (counts[word.difficulty] ?? 0) + 1;
    }
    return counts;
  }

  /// Fallback words nếu không load được file
  List<Word> _getFallbackWords() {
    return [
      const Word(original: "mẹ", meaning: "Người phụ nữ sinh ra mình", difficulty: 1),
      const Word(original: "cha", meaning: "Người đàn ông sinh ra mình", difficulty: 1),
      const Word(original: "nhà", meaning: "Nơi ở, chỗ sinh sống", difficulty: 1),
      const Word(original: "thắng", meaning: "Chiến thắng, đạt được thành công", difficulty: 2),
      const Word(original: "học", meaning: "Tiếp thu kiến thức", difficulty: 2),
      const Word(original: "thông minh", meaning: "Có trí tuệ, hiểu biết nhanh", difficulty: 3),
      const Word(original: "chăm chỉ", meaning: "Siêng năng, không lười biếng", difficulty: 3),
    ];
  }
}
