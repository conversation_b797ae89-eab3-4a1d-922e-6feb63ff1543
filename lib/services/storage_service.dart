import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/player.dart';

class StorageService {
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  static const String _playerStatsKey = 'player_stats';
  static const String _settingsKey = 'game_settings';
  static const String _dailyChallengeKey = 'daily_challenge';

  SharedPreferences? _prefs;

  /// Khởi tạo storage service
  Future<void> initialize() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// <PERSON><PERSON><PERSON> thống kê người chơi
  Future<void> savePlayerStats(PlayerStats stats) async {
    await initialize();
    final String jsonString = json.encode(stats.toJson());
    await _prefs!.setString(_playerStatsKey, jsonString);
  }

  /// L<PERSON><PERSON> thống kê người chơi
  Future<PlayerStats> getPlayerStats() async {
    await initialize();
    final String? jsonString = _prefs!.getString(_playerStatsKey);
    
    if (jsonString == null) {
      return const PlayerStats();
    }
    
    try {
      final Map<String, dynamic> jsonData = json.decode(jsonString);
      return PlayerStats.fromJson(jsonData);
    } catch (e) {
      // Nếu có lỗi parse thì trả về stats mặc định
      return const PlayerStats();
    }
  }

  /// Lưu cài đặt game
  Future<void> saveGameSettings(Map<String, dynamic> settings) async {
    await initialize();
    final String jsonString = json.encode(settings);
    await _prefs!.setString(_settingsKey, jsonString);
  }

  /// Lấy cài đặt game
  Future<Map<String, dynamic>> getGameSettings() async {
    await initialize();
    final String? jsonString = _prefs!.getString(_settingsKey);
    
    if (jsonString == null) {
      return _getDefaultSettings();
    }
    
    try {
      return Map<String, dynamic>.from(json.decode(jsonString));
    } catch (e) {
      return _getDefaultSettings();
    }
  }

  /// Cài đặt mặc định
  Map<String, dynamic> _getDefaultSettings() {
    return {
      'soundEnabled': true,
      'musicEnabled': true,
      'vibrationEnabled': true,
      'difficulty': 1, // 1: Dễ, 2: Vừa, 3: Khó
      'language': 'vi',
      'theme': 'light',
    };
  }

  /// Lưu thông tin daily challenge
  Future<void> saveDailyChallengeInfo({
    required DateTime date,
    required bool completed,
    required int score,
  }) async {
    await initialize();
    
    final Map<String, dynamic> challengeData = {
      'date': date.toIso8601String(),
      'completed': completed,
      'score': score,
    };
    
    final String jsonString = json.encode(challengeData);
    await _prefs!.setString(_dailyChallengeKey, jsonString);
  }

  /// Lấy thông tin daily challenge
  Future<Map<String, dynamic>?> getDailyChallengeInfo() async {
    await initialize();
    final String? jsonString = _prefs!.getString(_dailyChallengeKey);
    
    if (jsonString == null) return null;
    
    try {
      return Map<String, dynamic>.from(json.decode(jsonString));
    } catch (e) {
      return null;
    }
  }

  /// Kiểm tra xem hôm nay đã làm daily challenge chưa
  Future<bool> isDailyChallengeCompletedToday() async {
    final challengeInfo = await getDailyChallengeInfo();
    if (challengeInfo == null) return false;
    
    final DateTime challengeDate = DateTime.parse(challengeInfo['date']);
    final DateTime today = DateTime.now();
    
    return challengeDate.year == today.year &&
           challengeDate.month == today.month &&
           challengeDate.day == today.day &&
           challengeInfo['completed'] == true;
  }

  /// Lưu high score cho level cụ thể
  Future<void> saveHighScore(int level, int score) async {
    await initialize();
    final String key = 'high_score_level_$level';
    final int currentHighScore = _prefs!.getInt(key) ?? 0;
    
    if (score > currentHighScore) {
      await _prefs!.setInt(key, score);
    }
  }

  /// Lấy high score cho level cụ thể
  Future<int> getHighScore(int level) async {
    await initialize();
    final String key = 'high_score_level_$level';
    return _prefs!.getInt(key) ?? 0;
  }

  /// Lấy tất cả high scores
  Future<Map<int, int>> getAllHighScores() async {
    await initialize();
    final Map<int, int> highScores = {};
    
    final Set<String> keys = _prefs!.getKeys();
    for (String key in keys) {
      if (key.startsWith('high_score_level_')) {
        final String levelStr = key.replaceFirst('high_score_level_', '');
        final int? level = int.tryParse(levelStr);
        if (level != null) {
          highScores[level] = _prefs!.getInt(key) ?? 0;
        }
      }
    }
    
    return highScores;
  }

  /// Xóa tất cả dữ liệu
  Future<void> clearAllData() async {
    await initialize();
    await _prefs!.clear();
  }

  /// Lưu từ đã học
  Future<void> saveLearnedWord(String word) async {
    await initialize();
    const String key = 'learned_words';
    final List<String> learnedWords = _prefs!.getStringList(key) ?? [];
    
    if (!learnedWords.contains(word)) {
      learnedWords.add(word);
      await _prefs!.setStringList(key, learnedWords);
    }
  }

  /// Lấy danh sách từ đã học
  Future<List<String>> getLearnedWords() async {
    await initialize();
    const String key = 'learned_words';
    return _prefs!.getStringList(key) ?? [];
  }

  /// Lưu thời gian chơi tổng cộng (tính bằng giây)
  Future<void> addPlayTime(int seconds) async {
    await initialize();
    const String key = 'total_play_time';
    final int currentPlayTime = _prefs!.getInt(key) ?? 0;
    await _prefs!.setInt(key, currentPlayTime + seconds);
  }

  /// Lấy tổng thời gian chơi
  Future<int> getTotalPlayTime() async {
    await initialize();
    const String key = 'total_play_time';
    return _prefs!.getInt(key) ?? 0;
  }
}
