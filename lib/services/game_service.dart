import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import '../models/word.dart';
import '../models/game_state.dart';

import 'word_service.dart';
import 'high_score_service.dart';

class GameService extends ChangeNotifier {
  static final GameService _instance = GameService._internal();
  factory GameService() => _instance;
  GameService._internal();

  final WordService _wordService = WordService();
  GameState _gameState = const GameState();
  Timer? _gameTimer;
  
  GameState get gameState => _gameState;

  /// Khởi tạo game service
  Future<void> initialize() async {
    await _wordService.initialize();
  }

  /// Bắt đầu game mới
  Future<void> startNewGame(GameMode mode) async {
    // Dừng timer cũ nếu có
    _gameTimer?.cancel();
    
    // Lấy từ đầu tiên
    Word firstWord = _getNextWord(mode, 1);
    
    // Tạo state mới
    _gameState = GameState(
      mode: mode,
      status: GameStatus.playing,
      currentWord: firstWord,
      timeRemaining: _getInitialTime(mode),
      wordTimeRemaining: mode == GameMode.levelMode ? 20 : _getInitialTime(mode),
      level: 1,
      startTime: DateTime.now(),
      availableWords: _getWordsForMode(mode, 1),
    );
    
    // Bắt đầu timer
    _startTimer();
    
    notifyListeners();
  }

  /// Lấy thời gian ban đầu theo mode
  int _getInitialTime(GameMode mode) {
    switch (mode) {
      case GameMode.quickPlay:
        return 60; // 1 phút
      case GameMode.levelMode:
        return 300; // 5 phút tổng cho level mode (nhiều từ x 20s mỗi từ)
      case GameMode.dailyChallenge:
        return 180; // 3 phút
    }
  }

  /// Lấy danh sách từ cho mode
  List<Word> _getWordsForMode(GameMode mode, int level) {
    switch (mode) {
      case GameMode.quickPlay:
        return _wordService.allWords;
      case GameMode.levelMode:
        return _wordService.getWordsForLevel(level);
      case GameMode.dailyChallenge:
        return _wordService.allWords.where((w) => w.difficulty >= 2).toList();
    }
  }

  /// Lấy từ tiếp theo
  Word _getNextWord(GameMode mode, int level) {
    switch (mode) {
      case GameMode.quickPlay:
        return _wordService.getRandomWordExcluding(_gameState.completedWords);
      case GameMode.levelMode:
        List<Word> levelWords = _wordService.getWordsForLevel(level);
        List<Word> remainingWords = levelWords
            .where((w) => !_gameState.completedWords.contains(w))
            .toList();
        if (remainingWords.isEmpty) {
          remainingWords = levelWords;
        }
        return remainingWords[math.Random().nextInt(remainingWords.length)];
      case GameMode.dailyChallenge:
        return _wordService.getRandomWord(difficulty: math.Random().nextInt(2) + 2);
    }
  }

  /// Bắt đầu timer
  void _startTimer() {
    _gameTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      // Kiểm tra thời gian tổng
      if (_gameState.timeRemaining <= 0) {
        _endGame();
        return;
      }

      // Xử lý timer cho level mode
      if (_gameState.mode == GameMode.levelMode) {
        // Kiểm tra thời gian cho từ hiện tại
        if (_gameState.wordTimeRemaining <= 0) {
          _handleWordTimeout();
          return;
        }

        _gameState = _gameState.copyWith(
          timeRemaining: _gameState.timeRemaining - 1,
          wordTimeRemaining: _gameState.wordTimeRemaining - 1,
        );
      } else {
        // Các mode khác chỉ dùng thời gian tổng
        _gameState = _gameState.copyWith(
          timeRemaining: _gameState.timeRemaining - 1,
        );
      }

      notifyListeners();
    });
  }

  /// Cập nhật input của người chơi
  void updateUserInput(String input) {
    if (_gameState.status != GameStatus.playing) return;
    
    _gameState = _gameState.copyWith(userInput: input);
    notifyListeners();
  }

  /// Kiểm tra đáp án
  bool checkAnswer() {
    if (_gameState.currentWord == null || _gameState.status != GameStatus.playing) {
      return false;
    }

    bool isCorrect = _gameState.currentWord!.isCorrect(_gameState.userInput);
    
    if (isCorrect) {
      _handleCorrectAnswer();
    } else {
      _handleWrongAnswer();
    }
    
    return isCorrect;
  }

  /// Xử lý khi trả lời đúng
  void _handleCorrectAnswer() {
    if (_gameState.currentWord == null) return;
    
    // Tính điểm
    int baseScore = _gameState.currentWord!.baseScore;
    int timeBonus = _calculateTimeBonus();
    int streakBonus = _gameState.streakBonus;
    int levelBonus = _calculateLevelBonus();
    int totalScore = baseScore + timeBonus + streakBonus + levelBonus;
    
    // Cập nhật state
    List<Word> newCompletedWords = List.from(_gameState.completedWords)
      ..add(_gameState.currentWord!);
    
    _gameState = _gameState.copyWith(
      score: _gameState.score + totalScore,
      correctAnswers: _gameState.correctAnswers + 1,
      completedWords: newCompletedWords,
      streak: _gameState.streak + 1,
      userInput: '',
    );
    
    // Lấy từ tiếp theo hoặc chuyển level
    _moveToNextWord();
    
    notifyListeners();
  }

  /// Xử lý khi trả lời sai
  void _handleWrongAnswer() {
    _gameState = _gameState.copyWith(
      wrongAnswers: _gameState.wrongAnswers + 1,
      streak: 0,
      userInput: '',
    );

    // Nếu sai quá 3 lần thì game over (tùy chọn)
    if (_gameState.wrongAnswers >= 3 && _gameState.mode == GameMode.levelMode) {
      _endGame();
      return;
    }

    notifyListeners();
  }

  /// Xử lý khi hết thời gian cho từ hiện tại (chỉ dành cho level mode)
  void _handleWordTimeout() {
    if (_gameState.mode != GameMode.levelMode) return;

    // Tính như trả lời sai
    _gameState = _gameState.copyWith(
      wrongAnswers: _gameState.wrongAnswers + 1,
      streak: 0,
      userInput: '',
    );

    // Kết thúc game khi hết thời gian cho từ
    _endGame();

    notifyListeners();
  }

  /// Tính điểm thưởng thời gian
  int _calculateTimeBonus() {
    switch (_gameState.mode) {
      case GameMode.quickPlay:
        return _gameState.timeRemaining * 2; // 2 điểm/giây còn lại tổng
      case GameMode.levelMode:
        return _gameState.wordTimeRemaining * 5; // 5 điểm/giây còn lại cho từ (cao hơn vì khó hơn)
      case GameMode.dailyChallenge:
        return _gameState.timeRemaining * 3; // 3 điểm/giây còn lại
    }
  }

  /// Tính điểm thưởng level (chỉ cho Level Mode)
  int _calculateLevelBonus() {
    if (_gameState.mode == GameMode.levelMode) {
      return _gameState.level * 100; // 100 điểm/level
    }
    return 0;
  }

  /// Chuyển sang từ tiếp theo
  void _moveToNextWord() {
    // Kiểm tra xem có cần chuyển level không
    if (_shouldLevelUp()) {
      _levelUp();
      return;
    }

    // Lấy từ tiếp theo
    Word nextWord = _getNextWord(_gameState.mode, _gameState.level);

    // Reset thời gian cho từ mới nếu là level mode
    int newWordTime = _gameState.mode == GameMode.levelMode ? 20 : _gameState.wordTimeRemaining;

    _gameState = _gameState.copyWith(
      currentWord: nextWord,
      wordTimeRemaining: newWordTime,
    );
  }

  /// Kiểm tra có nên lên level không
  bool _shouldLevelUp() {
    if (_gameState.mode != GameMode.levelMode) return false;
    
    List<Word> levelWords = _wordService.getWordsForLevel(_gameState.level);
    int completedInLevel = _gameState.completedWords
        .where((w) => levelWords.contains(w))
        .length;
    
    return completedInLevel >= (levelWords.length * 0.7); // 70% từ trong level
  }

  /// Lên level
  void _levelUp() {
    int newLevel = _gameState.level + 1;
    Word nextWord = _getNextWord(_gameState.mode, newLevel);

    _gameState = _gameState.copyWith(
      level: newLevel,
      currentWord: nextWord,
      timeRemaining: _gameState.timeRemaining + 30, // Thưởng thêm 30 giây
      wordTimeRemaining: _gameState.mode == GameMode.levelMode ? 20 : _gameState.wordTimeRemaining, // Reset thời gian từ
      availableWords: _getWordsForMode(_gameState.mode, newLevel),
    );
  }

  /// Kết thúc game
  void _endGame() {
    _gameTimer?.cancel();
    _gameState = _gameState.copyWith(status: GameStatus.gameOver);
    notifyListeners();
  }

  /// Kiểm tra xem có phải high score không
  bool isHighScore() {
    if (_gameState.score <= 0) return false;

    // Kiểm tra high score theo chế độ chơi cụ thể
    String difficulty = _getDifficultyString();
    return HighScoreService.instance.isHighScoreByDifficulty(_gameState.score, difficulty);
  }

  /// Lấy thông tin để tạo high score
  Map<String, dynamic> getHighScoreData() {
    return {
      'score': _gameState.score,
      'level': _gameState.level,
      'difficulty': _getDifficultyString(),
      'wordsCompleted': _gameState.correctAnswers,
      'accuracy': (_gameState.correctAnswers + _gameState.wrongAnswers) > 0
          ? _gameState.correctAnswers / (_gameState.correctAnswers + _gameState.wrongAnswers)
          : 0.0,
      'timeSpent': _getInitialTime(_gameState.mode) - _gameState.timeRemaining,
    };
  }

  /// Lấy tên độ khó dựa trên mode và level
  String _getDifficultyString() {
    switch (_gameState.mode) {
      case GameMode.quickPlay:
        return 'Chơi nhanh';
      case GameMode.levelMode:
        return 'Chơi theo cấp';
      case GameMode.dailyChallenge:
        return 'Thử thách hàng ngày';
    }
  }



  /// Tạm dừng game
  void pauseGame() {
    if (_gameState.status == GameStatus.playing) {
      _gameTimer?.cancel();
      _gameState = _gameState.copyWith(status: GameStatus.paused);
      notifyListeners();
    }
  }

  /// Tiếp tục game
  void resumeGame() {
    if (_gameState.status == GameStatus.paused) {
      _gameState = _gameState.copyWith(status: GameStatus.playing);
      _startTimer();
      notifyListeners();
    }
  }

  /// Lấy gợi ý
  String getHint() {
    if (_gameState.currentWord == null) return '';
    
    String original = _gameState.currentWord!.original;
    if (original.length <= 2) return original[0];
    
    // Hiển thị chữ cái đầu và cuối
    return '${original[0]}${'_' * (original.length - 2)}${original[original.length - 1]}';
  }

  /// Xáo trộn lại từ hiện tại
  String getScrambledWord() {
    if (_gameState.currentWord == null) return '';
    return _gameState.currentWord!.scrambled;
  }

  /// Reset game
  void resetGame() {
    _gameTimer?.cancel();
    _gameState = const GameState();
    notifyListeners();
  }

  @override
  void dispose() {
    _gameTimer?.cancel();
    super.dispose();
  }
}
