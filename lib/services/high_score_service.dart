import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/high_score.dart';

class HighScoreService {
  static const String _highScoresKey = 'high_scores';
  static const int _maxHighScores = 10;
  
  static HighScoreService? _instance;
  static HighScoreService get instance => _instance ??= HighScoreService._();
  
  HighScoreService._();

  List<HighScore> _highScores = [];
  List<HighScore> get highScores => List.unmodifiable(_highScores);

  // Initialize and load high scores
  Future<void> initialize() async {
    await _loadHighScores();
  }

  // Load high scores from storage
  Future<void> _loadHighScores() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final highScoresJson = prefs.getStringList(_highScoresKey) ?? [];
      
      _highScores = highScoresJson
          .map((jsonString) => HighScore.fromJson(json.decode(jsonString)))
          .toList();
      
      // Sort by score descending
      _highScores.sort((a, b) => b.score.compareTo(a.score));
    } catch (e) {
      _highScores = [];
    }
  }

  // Save high scores to storage
  Future<void> _saveHighScores() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final highScoresJson = _highScores
          .map((score) => json.encode(score.toJson()))
          .toList();
      
      await prefs.setStringList(_highScoresKey, highScoresJson);
    } catch (e) {
      // Ignore save errors
    }
  }

  // Add a new high score
  Future<bool> addHighScore(HighScore newScore) async {
    _highScores.add(newScore);
    
    // Sort by score descending
    _highScores.sort((a, b) => b.score.compareTo(a.score));
    
    // Keep only top scores
    if (_highScores.length > _maxHighScores) {
      _highScores = _highScores.take(_maxHighScores).toList();
    }
    
    await _saveHighScores();
    
    // Return true if the new score made it to the top list
    return _highScores.contains(newScore);
  }

  // Check if a score qualifies for high score list
  bool isHighScore(int score) {
    if (_highScores.length < _maxHighScores) {
      return true;
    }
    return score > _highScores.last.score;
  }

  // Check if a score qualifies for high score list by difficulty/mode
  bool isHighScoreByDifficulty(int score, String difficulty) {
    final difficultyScores = getHighScoresByDifficulty(difficulty);
    if (difficultyScores.length < _maxHighScores) {
      return true;
    }
    return difficultyScores.isEmpty || score > difficultyScores.last.score;
  }

  // Get high scores by difficulty
  List<HighScore> getHighScoresByDifficulty(String difficulty) {
    return _highScores
        .where((score) => score.difficulty == difficulty)
        .toList();
  }

  // Get personal best score
  HighScore? getPersonalBest(String playerName) {
    final personalScores = _highScores
        .where((score) => score.playerName == playerName)
        .toList();
    
    if (personalScores.isEmpty) return null;
    
    personalScores.sort((a, b) => b.score.compareTo(a.score));
    return personalScores.first;
  }

  // Get rank of a specific score
  int getRank(int score) {
    int rank = 1;
    for (final highScore in _highScores) {
      if (score >= highScore.score) {
        break;
      }
      rank++;
    }
    return rank;
  }

  // Clear all high scores
  Future<void> clearHighScores() async {
    _highScores.clear();
    await _saveHighScores();
  }

  // Get statistics
  Map<String, dynamic> getStatistics() {
    if (_highScores.isEmpty) {
      return {
        'totalGames': 0,
        'averageScore': 0.0,
        'highestScore': 0,
        'averageAccuracy': 0.0,
        'totalWordsCompleted': 0,
      };
    }

    final totalGames = _highScores.length;
    final averageScore = _highScores
        .map((score) => score.score)
        .reduce((a, b) => a + b) / totalGames;
    final highestScore = _highScores.first.score;
    final averageAccuracy = _highScores
        .map((score) => score.accuracy)
        .reduce((a, b) => a + b) / totalGames;
    final totalWordsCompleted = _highScores
        .map((score) => score.wordsCompleted)
        .reduce((a, b) => a + b);

    return {
      'totalGames': totalGames,
      'averageScore': averageScore,
      'highestScore': highestScore,
      'averageAccuracy': averageAccuracy,
      'totalWordsCompleted': totalWordsCompleted,
    };
  }
}
