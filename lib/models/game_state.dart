import 'word.dart';

enum GameMode {
  quickPlay, // Ch<PERSON><PERSON> nhanh 60 giây
  levelMode, // Ch<PERSON>i theo cấp độ
  dailyChallenge, // Th<PERSON> thách hàng ngày
}

enum GameStatus {
  notStarted,
  playing,
  paused,
  completed,
  gameOver,
}

class GameState {
  final GameMode mode;
  final GameStatus status;
  final Word? currentWord;
  final String userInput;
  final int score;
  final int level;
  final int timeRemaining; // giây
  final int wordTimeRemaining; // giây còn lại cho từ hiện tại (dành cho level mode)
  final int correctAnswers;
  final int wrongAnswers;
  final List<Word> completedWords;
  final List<Word> availableWords;
  final int streak; // Chuỗi trả lời đúng liên tiếp
  final DateTime? startTime;

  const GameState({
    this.mode = GameMode.quickPlay,
    this.status = GameStatus.notStarted,
    this.currentWord,
    this.userInput = '',
    this.score = 0,
    this.level = 1,
    this.timeRemaining = 60,
    this.wordTimeRemaining = 20,
    this.correctAnswers = 0,
    this.wrongAnswers = 0,
    this.completedWords = const [],
    this.availableWords = const [],
    this.streak = 0,
    this.startTime,
  });

  /// Tạo bản sao với các thay đổi
  GameState copyWith({
    GameMode? mode,
    GameStatus? status,
    Word? currentWord,
    String? userInput,
    int? score,
    int? level,
    int? timeRemaining,
    int? wordTimeRemaining,
    int? correctAnswers,
    int? wrongAnswers,
    List<Word>? completedWords,
    List<Word>? availableWords,
    int? streak,
    DateTime? startTime,
  }) {
    return GameState(
      mode: mode ?? this.mode,
      status: status ?? this.status,
      currentWord: currentWord ?? this.currentWord,
      userInput: userInput ?? this.userInput,
      score: score ?? this.score,
      level: level ?? this.level,
      timeRemaining: timeRemaining ?? this.timeRemaining,
      wordTimeRemaining: wordTimeRemaining ?? this.wordTimeRemaining,
      correctAnswers: correctAnswers ?? this.correctAnswers,
      wrongAnswers: wrongAnswers ?? this.wrongAnswers,
      completedWords: completedWords ?? this.completedWords,
      availableWords: availableWords ?? this.availableWords,
      streak: streak ?? this.streak,
      startTime: startTime ?? this.startTime,
    );
  }

  /// Tính tỷ lệ chính xác
  double get accuracy {
    int total = correctAnswers + wrongAnswers;
    if (total == 0) return 0.0;
    return correctAnswers / total;
  }

  /// Kiểm tra xem game có kết thúc không
  bool get isGameOver {
    return status == GameStatus.gameOver || 
           status == GameStatus.completed ||
           timeRemaining <= 0;
  }

  /// Tính điểm thưởng streak
  int get streakBonus {
    if (streak < 3) return 0;
    if (streak < 5) return 50;
    if (streak < 10) return 100;
    return 200;
  }

  @override
  String toString() {
    return 'GameState(mode: $mode, status: $status, score: $score, level: $level)';
  }
}
