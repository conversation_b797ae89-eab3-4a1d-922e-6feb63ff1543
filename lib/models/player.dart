class PlayerStats {
  final int totalGamesPlayed;
  final int totalScore;
  final int highScore;
  final int totalWordsCompleted;
  final double averageAccuracy;
  final int currentStreak;
  final int longestStreak;
  final DateTime? lastPlayedDate;
  final Map<int, int> levelProgress; // level -> best score
  final List<String> learnedWords;

  const PlayerStats({
    this.totalGamesPlayed = 0,
    this.totalScore = 0,
    this.highScore = 0,
    this.totalWordsCompleted = 0,
    this.averageAccuracy = 0.0,
    this.currentStreak = 0,
    this.longestStreak = 0,
    this.lastPlayedDate,
    this.levelProgress = const {},
    this.learnedWords = const [],
  });

  /// Tạo bản sao với các thay đổi
  PlayerStats copyWith({
    int? totalGamesPlayed,
    int? totalScore,
    int? highScore,
    int? totalWordsCompleted,
    double? averageAccuracy,
    int? currentStreak,
    int? longestStreak,
    DateTime? lastPlayedDate,
    Map<int, int>? levelProgress,
    List<String>? learnedWords,
  }) {
    return PlayerStats(
      totalGamesPlayed: totalGamesPlayed ?? this.totalGamesPlayed,
      totalScore: totalScore ?? this.totalScore,
      highScore: highScore ?? this.highScore,
      totalWordsCompleted: totalWordsCompleted ?? this.totalWordsCompleted,
      averageAccuracy: averageAccuracy ?? this.averageAccuracy,
      currentStreak: currentStreak ?? this.currentStreak,
      longestStreak: longestStreak ?? this.longestStreak,
      lastPlayedDate: lastPlayedDate ?? this.lastPlayedDate,
      levelProgress: levelProgress ?? this.levelProgress,
      learnedWords: learnedWords ?? this.learnedWords,
    );
  }

  /// Cập nhật thống kê sau khi hoàn thành game
  PlayerStats updateAfterGame({
    required int gameScore,
    required int wordsCompleted,
    required double gameAccuracy,
    required int gameStreak,
    required int level,
  }) {
    int newTotalGames = totalGamesPlayed + 1;
    int newTotalScore = totalScore + gameScore;
    int newHighScore = gameScore > highScore ? gameScore : highScore;
    int newTotalWords = totalWordsCompleted + wordsCompleted;
    
    // Tính accuracy trung bình mới
    double newAverageAccuracy = ((averageAccuracy * totalGamesPlayed) + gameAccuracy) / newTotalGames;
    
    // Cập nhật streak
    int newCurrentStreak = gameStreak;
    int newLongestStreak = gameStreak > longestStreak ? gameStreak : longestStreak;
    
    // Cập nhật level progress
    Map<int, int> newLevelProgress = Map.from(levelProgress);
    if (!newLevelProgress.containsKey(level) || gameScore > newLevelProgress[level]!) {
      newLevelProgress[level] = gameScore;
    }

    return copyWith(
      totalGamesPlayed: newTotalGames,
      totalScore: newTotalScore,
      highScore: newHighScore,
      totalWordsCompleted: newTotalWords,
      averageAccuracy: newAverageAccuracy,
      currentStreak: newCurrentStreak,
      longestStreak: newLongestStreak,
      lastPlayedDate: DateTime.now(),
      levelProgress: newLevelProgress,
    );
  }

  /// Thêm từ đã học
  PlayerStats addLearnedWord(String word) {
    if (learnedWords.contains(word)) return this;
    
    List<String> newLearnedWords = List.from(learnedWords)..add(word);
    return copyWith(learnedWords: newLearnedWords);
  }

  /// Chuyển đổi thành Map để lưu trữ
  Map<String, dynamic> toJson() {
    return {
      'totalGamesPlayed': totalGamesPlayed,
      'totalScore': totalScore,
      'highScore': highScore,
      'totalWordsCompleted': totalWordsCompleted,
      'averageAccuracy': averageAccuracy,
      'currentStreak': currentStreak,
      'longestStreak': longestStreak,
      'lastPlayedDate': lastPlayedDate?.toIso8601String(),
      'levelProgress': levelProgress,
      'learnedWords': learnedWords,
    };
  }

  /// Tạo PlayerStats từ Map
  factory PlayerStats.fromJson(Map<String, dynamic> json) {
    return PlayerStats(
      totalGamesPlayed: json['totalGamesPlayed'] ?? 0,
      totalScore: json['totalScore'] ?? 0,
      highScore: json['highScore'] ?? 0,
      totalWordsCompleted: json['totalWordsCompleted'] ?? 0,
      averageAccuracy: (json['averageAccuracy'] ?? 0.0).toDouble(),
      currentStreak: json['currentStreak'] ?? 0,
      longestStreak: json['longestStreak'] ?? 0,
      lastPlayedDate: json['lastPlayedDate'] != null 
          ? DateTime.parse(json['lastPlayedDate']) 
          : null,
      levelProgress: Map<int, int>.from(json['levelProgress'] ?? {}),
      learnedWords: List<String>.from(json['learnedWords'] ?? []),
    );
  }

  @override
  String toString() {
    return 'PlayerStats(games: $totalGamesPlayed, highScore: $highScore, accuracy: ${(averageAccuracy * 100).toStringAsFixed(1)}%)';
  }
}
