class HighScore {
  final String playerName;
  final int score;
  final int level;
  final String difficulty;
  final DateTime date;
  final int wordsCompleted;
  final double accuracy;
  final int timeSpent; // in seconds

  const HighScore({
    required this.playerName,
    required this.score,
    required this.level,
    required this.difficulty,
    required this.date,
    required this.wordsCompleted,
    required this.accuracy,
    required this.timeSpent,
  });

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'playerName': playerName,
      'score': score,
      'level': level,
      'difficulty': difficulty,
      'date': date.toIso8601String(),
      'wordsCompleted': wordsCompleted,
      'accuracy': accuracy,
      'timeSpent': timeSpent,
    };
  }

  // Create from JSON
  factory HighScore.fromJson(Map<String, dynamic> json) {
    return HighScore(
      playerName: json['playerName'] ?? 'Người chơi',
      score: json['score'] ?? 0,
      level: json['level'] ?? 1,
      difficulty: json['difficulty'] ?? 'Dễ',
      date: DateTime.parse(json['date'] ?? DateTime.now().toIso8601String()),
      wordsCompleted: json['wordsCompleted'] ?? 0,
      accuracy: (json['accuracy'] ?? 0.0).toDouble(),
      timeSpent: json['timeSpent'] ?? 0,
    );
  }

  // Format time for display
  String get formattedTime {
    final minutes = timeSpent ~/ 60;
    final seconds = timeSpent % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  // Format accuracy for display
  String get formattedAccuracy {
    return '${(accuracy * 100).toStringAsFixed(1)}%';
  }

  // Format date for display
  String get formattedDate {
    return '${date.day}/${date.month}/${date.year}';
  }

  @override
  String toString() {
    return 'HighScore(playerName: $playerName, score: $score, level: $level, difficulty: $difficulty)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is HighScore &&
        other.playerName == playerName &&
        other.score == score &&
        other.level == level &&
        other.difficulty == difficulty &&
        other.date == date;
  }

  @override
  int get hashCode {
    return Object.hash(playerName, score, level, difficulty, date);
  }
}
