class Word {
  final String original;
  final String meaning;
  final int difficulty; // 1: <PERSON><PERSON>, 2: V<PERSON><PERSON>, 3: Khó
  final List<String> examples;

  const Word({
    required this.original,
    required this.meaning,
    required this.difficulty,
    this.examples = const [],
  });

  /// Tạo từ xáo trộn từ từ gốc
  String get scrambled {
    List<String> chars = original.split('');
    chars.shuffle();
    return chars.join('');
  }

  /// Kiểm tra xem từ người dùng nhập có đúng không
  bool isCorrect(String userInput) {
    return userInput.toLowerCase().trim() == original.toLowerCase().trim();
  }

  /// Tính điểm dựa trên độ khó và độ dài từ
  int get baseScore {
    int lengthScore = original.length * 10;
    int difficultyMultiplier = difficulty;
    return lengthScore * difficultyMultiplier;
  }

  /// Chuyển đổi thành Map để lưu trữ
  Map<String, dynamic> toJson() {
    return {
      'original': original,
      'meaning': meaning,
      'difficulty': difficulty,
      'examples': examples,
    };
  }

  /// Tạo Word từ Map
  factory Word.fromJson(Map<String, dynamic> json) {
    return Word(
      original: json['original'] as String,
      meaning: json['meaning'] as String,
      difficulty: json['difficulty'] as int,
      examples: List<String>.from(json['examples'] ?? []),
    );
  }

  @override
  String toString() {
    return 'Word(original: $original, meaning: $meaning, difficulty: $difficulty)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Word && other.original == original;
  }

  @override
  int get hashCode => original.hashCode;
}
