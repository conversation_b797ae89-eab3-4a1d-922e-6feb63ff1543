{"buildFiles": ["/Users/<USER>/fvm/versions/3.29.3/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/word_jumble/android/app/.cxx/RelWithDebInfo/691n3o6a/armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/word_jumble/android/app/.cxx/RelWithDebInfo/691n3o6a/armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}